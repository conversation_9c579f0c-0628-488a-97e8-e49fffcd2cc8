<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">


    <TabHost
        android:id="@android:id/tabhost"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TabWidget
                android:id="@android:id/tabs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <FrameLayout
                android:id="@android:id/tabcontent"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.example.timer.AlarmView
                    android:id="@+id/tabAlarm"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                   <ListView
                       android:id="@+id/lvAlarmList"
                       android:layout_weight="1"
                       android:layout_width="fill_parent"
                       android:layout_height="0dp">
                   </ListView>

                    <Button
                        android:id="@+id/btnAddAlarm"
                        android:layout_marginBottom="@dimen/cardview_compat_inset_shadow"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:background="@android:drawable/ic_menu_add" />


                </com.example.timer.AlarmView>

                <LinearLayout
                    android:id="@+id/tabTime"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                        android:id="@+id/dClock"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:textSize="40sp"
                        android:layout_gravity="center" />



                </LinearLayout>
                <com.example.timer.TimerView
                    android:id="@+id/tabTimer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">
                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal">


                        <EditText
                            android:id="@+id/etHour"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="number"
                            android:singleLine="true"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=":"/>
                        <EditText
                            android:id="@+id/etMin"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="number"
                            android:singleLine="true"/>
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=":"/>
                        <EditText
                            android:id="@+id/etSec"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="number"
                            android:singleLine="true"/>

                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/btnGroup"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <Button
                            android:id="@+id/btnStart"
                            android:text="开始"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"/>
                        <Button
                            android:id="@+id/btnPause"
                            android:text="暂停"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"/>
                        <Button
                            android:id="@+id/btnReset"
                            android:text="重置"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"/>

                    </LinearLayout>
                </com.example.timer.TimerView>


                <com.example.timer.StopWatch
                    android:id="@+id/tabStopWatch"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">


                    <Chronometer
                        android:id="@+id/myChronometer"
                        android:layout_width="fill_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:textSize="40dp"
                        android:gravity="center_horizontal"/>


                    <LinearLayout
                        android:id="@+id/btnGroup2"
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <Button
                            android:id="@+id/butStart"
                            android:text="开始"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"/>
                        <Button
                            android:id="@+id/butPause"
                            android:text="暂停"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"/>
                           <Button
                            android:id="@+id/butResume"
                            android:text="复位"
                           android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"/>
                        <Button
                            android:id="@+id/butReset"
                            android:text="重置"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content"/>

                    </LinearLayout>
                </com.example.timer.StopWatch>

            </FrameLayout>
        </LinearLayout>
    </TabHost>
</androidx.constraintlayout.widget.ConstraintLayout>